# ⚙️ 配置指南

### 🎨 主题系统配置

#### 可用主题

| 主题名称 | 主题ID | 特色描述 |
|---------|--------|----------|
| 日光象牙白 | `ivory-light` | 温暖舒适的浅色主题 |
| 夜月玄玉黑 | `dark-obsidian` | 深邃优雅的深色主题 |
| 清雅茉莉绿 | `jasmine-green` | 茉莉花香·禅意美学·护眼悦目 |
| 深邃海军蓝 | `navy-blue` | 沉稳专业的蓝色主题 |

#### 时间主题配置

编辑 `js/config.js` 文件：

```javascript
window.NavSphereConfig = {
    timeTheme: {
        enabled: true,           // 启用时间主题
        lightTheme: 'ivory-light',  // 浅色时段使用的主题
        darkTheme: 'dark-obsidian', // 深色时段使用的主题
        lightStart: 6,           // 浅色主题开始时间 (6:00)
        lightEnd: 18,            // 浅色主题结束时间 (18:00)
    }
};
```

#### 预设配置

| 预设名称 | 浅色时间段 | 适用场景 |
|---------|-----------|----------|
| `standard` | 6:00-18:00 | 标准作息 (默认) |
| `earlyBird` | 5:00-19:00 | 早起型用户 |
| `nightOwl` | 8:00-20:00 | 夜猫子型用户 |
| `office` | 9:00-17:00 | 办公时间 |

#### 快速配置API

```javascript
// 应用预设
applyTimeThemePreset("nightOwl")

// 自定义时间段
NavSphereQuickConfig.setTimeTheme(7, 19)

// 手动切换主题
NavApp.theme.setTheme('jasmine-green')

// 查看当前配置
NavApp.theme.getInfo()
```

### 📊 数据配置

#### 多文件配置系统

从 v2.0 开始，支持通过 `nav/data/appconfig.json` 配置多个数据源文件：

```json
{
  "version": "2.0",
  "description": "FaciShare 导航数据配置",
  "dataSources": [
    {
      "id": "main",
      "name": "主要网站数据",
      "path": "data/sites.json",
      "enabled": true,
      "priority": 1,
      "description": "包含主要的企业内部工具和服务"
    },
    {
      "id": "external",
      "name": "外部工具数据",
      "path": "data/external-sites.json",
      "enabled": true,
      "priority": 2,
      "description": "第三方工具和外部服务"
    }
  ],
  "mergeStrategy": {
    "duplicateHandling": "merge",
    "categoryMerging": "append",
    "siteIdConflict": "keepFirst"
  }
}
```

**配置说明**：
- `dataSources`: 数据源配置数组
  - `id`: 数据源唯一标识
  - `name`: 数据源显示名称
  - `path`: JSON文件路径（相对于项目根目录）
  - `enabled`: 是否启用此数据源
  - `priority`: 加载优先级（数字越小优先级越高）
  - `description`: 数据源描述
  - `domains` (可选): **域名绑定数组**。用于实现多租户或多环境部署。
    - **工作机制**:
      - 当此字段**存在且不为空**时（如 `["tenant-a.com", "www.tenant-a.com"]`），只有当导航页的当前域名完全匹配数组中的某个域名时，此数据源才会被加载。
      - 当此字段**不存在、为`null`或为空数组`[]`**时，此数据源被视为**默认数据源**，只要 `enabled` 为 `true`，它就会在所有域名下加载。
    - **用例**:
      - **多租户部署**: 为不同客户的域名配置不同的数据源，实现定制化内容。
      - **环境隔离**: 为开发环境（如 `localhost`, `127.0.0.1`）和生产环境配置不同的数据源。
- `mergeStrategy`: 数据合并策略
  - `duplicateHandling`: 重复数据处理方式（`merge`/`skip`/`replace`）
  - `categoryMerging`: 分类合并方式（`append`/`merge`）
  - `siteIdConflict`: 网站ID冲突处理（`keepFirst`/`keepLast`/`merge`）

#### 基础网站配置

编辑 `data/sites.json` 文件：

```json
{
  "categories": [
    {
      "id": "development",
      "name": "开发工具",
      "icon": "💻",
      "description": "开发相关的工具和资源",
      "sites": [
        {
          "id": "vscode",
          "name": "Visual Studio Code",
          "description": "微软开发的免费代码编辑器",
          "icon": "📝",
          "url": "https://code.visualstudio.com",
          "tags": ["编辑器", "开发", "微软", "免费"],
          "featured": true
        }
      ]
    }
  ]
}
```

#### 多级分类结构

```json
{
  "id": "programming",
  "name": "编程学习",
  "icon": "📚",
  "children": [
    {
      "id": "languages",
      "name": "编程语言",
      "children": [
        {
          "id": "javascript",
          "name": "JavaScript",
          "sites": []
        }
      ]
    }
  ]
}
```

#### 字段说明

**分类字段**：

- `id` (必需) - 唯一标识符，用于URL和内部引用
- `name` (必需) - 显示名称
- `icon` (可选) - 分类图标，支持Emoji或图标文件
- `description` (可选) - 分类描述
- `sites` (可选) - 直接包含的网站列表
- `children` (可选) - 子分类列表，支持3级嵌套
- `order` (可选) - 排序权重，数字越小越靠前

**网站字段**：

- `id` (必需) - 唯一标识符
- `name` (必需) - 网站名称
- `url` (可选) - 网站链接，支持与markdownFile同时存在
- `description` (可选) - 网站描述
- `icon` (可选) - 网站图标
- `tags` (可选) - 标签数组，用于搜索和分类
- `markdownFile` (可选) - Markdown文档路径，支持与url同时存在
- `featured` (可选) - 是否为推荐网站

#### 🔗 多链接卡片功能

每个网站卡片支持三种链接模式的灵活组合：

**情况一：仅网址链接**
```json
{
  "id": "example-site",
  "name": "示例网站",
  "description": "这是一个外部网站",
  "icon": "🌐",
  "url": "https://example.com",
  "tags": ["工具", "外部"]
}
```
- 点击卡片直接打开外部网址
- 卡片显示为外部链接样式（绿色边框指示器）

**情况二：仅文档链接**
```json
{
  "id": "local-doc",
  "name": "本地文档",
  "description": "这是一个本地Markdown文档",
  "icon": "📄",
  "markdownFile": "nav/data/docs/guide.md",
  "tags": ["文档", "指南"]
}
```
- 点击卡片直接预览Markdown文档
- 卡片显示为文档样式（橙色边框指示器）
- 右上角显示"📚 文档"徽章

**情况三：网址+文档双链接**
```json
{
  "id": "dual-link-site",
  "name": "双链接网站",
  "description": "同时支持外部访问和本地文档",
  "icon": "🔗",
  "url": "https://example.com",
  "markdownFile": "nav/data/docs/example-guide.md",
  "tags": ["工具", "文档"]
}
```
- 点击卡片主体打开外部网址
- 右上角显示"文档"指示器，点击预览Markdown文档
- 卡片显示为混合样式（蓝色边框指示器）

**交互设计特点**：
- 🎯 **智能识别** - 系统自动根据字段配置判断卡片类型
- 🎨 **视觉区分** - 不同类型卡片使用不同颜色的边框指示器
- 👆 **精确点击** - 文档指示器支持独立点击，不影响主卡片功能
- 📱 **移动适配** - 紧凑模式下文档指示器自动调整为圆形图标
- ⚡ **性能优化** - 事件冒泡处理，避免点击冲突

#### 图标系统

支持多种图标类型：

```json
{
  "icon": "🔧",
  "icon": "assets/icons/github.svg",
  "icon": "https://example.com/icon.svg",
  "icon": "https://favicon.ico",
  "iconClass": "fas fa-code"
}
```

**图标字段说明**：
- `icon`：支持 `Unicode字符（Emoji）`、`本地SVG文件`、`远程图片链接`、`远程favicon`
- `iconClass`：**新增** Font Awesome 图标类名，优先级高于 `icon` 字段

#### Font Awesome 图标库支持

项目内置 Font Awesome 7.0.0，支持使用 `iconClass` 字段配置专业图标：

```json
{
  "id": "github",
  "name": "GitHub",
  "description": "全球最大的代码托管平台",
  "icon": "🐙",
  "iconClass": "fab fa-github",
  "url": "https://github.com/",
  "tags": ["代码", "开发", "git"]
}
```

**常用图标示例**：
- `fas fa-code` - 代码图标
- `fab fa-github` - GitHub 品牌图标
- `fas fa-book` - 书籍/文档图标
- `fas fa-envelope` - 邮件图标
- `fas fa-tasks` - 任务管理图标
- `fas fa-calendar-alt` - 日历图标

**优先级规则**：
1. 如果提供了 `iconClass`，优先显示 Font Awesome 图标
2. 如果 `iconClass` 为空，则使用 `icon` 字段
3. 如果都为空，显示默认图标 🌐

**特性**：
- 智能识别图标类型
- 加载失败自动回退
- 支持懒加载优化
- 主题适配调整
- **新增** Font Awesome 图标库支持

#### Markdown文档

支持本地Markdown文档渲染，可与网址链接组合使用：

```json
{
  "id": "git-guide",
  "name": "Git使用指南",
  "description": "详细的Git命令和最佳实践",
  "icon": "📖",
  "markdownFile": "nav/data/docs/git-guide.md",
  "tags": ["Git", "教程", "文档"]
}
```

**Markdown功能特性**：
- 📖 **完整渲染** - 支持标题、列表、代码块、表格、链接等
- 🎨 **主题适配** - 文档样式自动适配当前主题
- 📱 **响应式** - 移动端优化的阅读体验
- ⚡ **快速加载** - 本地文档，无网络依赖

#### 🎯 多链接卡片使用场景

**开发工具类**：
```json
{
  "id": "vscode",
  "name": "Visual Studio Code",
  "description": "微软开发的免费代码编辑器",
  "icon": "💻",
  "url": "https://code.visualstudio.com",
  "markdownFile": "nav/data/docs/vscode-guide.md",
  "tags": ["编辑器", "开发工具"]
}
```
- 点击卡片：访问官网下载或使用
- 点击文档：查看使用指南和配置说明

**学习资源类**：
```json
{
  "id": "javascript-tutorial",
  "name": "JavaScript 教程",
  "description": "从入门到精通的JavaScript学习资源",
  "icon": "📚",
  "url": "https://javascript.info",
  "markdownFile": "nav/data/docs/js-notes.md",
  "tags": ["JavaScript", "教程", "前端"]
}
```
- 点击卡片：访问在线教程网站
- 点击文档：查看个人学习笔记和总结

**企业内部工具**：
```json
{
  "id": "internal-system",
  "name": "内部管理系统",
  "description": "公司内部使用的管理系统",
  "icon": "🏢",
  "url": "https://internal.company.com",
  "markdownFile": "nav/data/docs/system-manual.md",
  "tags": ["内部工具", "管理"]
}
```
- 点击卡片：访问内部系统
- 点击文档：查看使用手册和操作指南

#### 数据验证

使用以下脚本验证数据格式：

```javascript
// 在浏览器控制台运行
fetch('./data/sites.json')
  .then(r => r.json())
  .then(data => {
    console.log('✅ JSON格式正确');

    // 检查必需字段
    const errors = [];
    data.categories.forEach(cat => {
      if (!cat.id || !cat.name) {
        errors.push(`分类缺少必需字段: ${cat.name || cat.id}`);
      }

      if (cat.sites) {
        cat.sites.forEach(site => {
          if (!site.id || !site.name || !site.url) {
            errors.push(`网站缺少必需字段: ${site.name || site.id}`);
          }
        });
      }
    });

    if (errors.length === 0) {
      console.log('✅ 数据验证通过');
    } else {
      console.error('❌ 数据验证失败:', errors);
    }
  })
  .catch(e => console.error('❌ JSON格式错误:', e));
```

### 支持的分类结构

```json
{
  "id": "parent-category",
  "name": "父分类",
  "icon": "🔧",
  "children": [
    {
      "id": "child-category",
      "name": "子分类",
      "sites": [
        {
          "id": "site-id",
          "name": "网站名称",
          "description": "网站描述",
          "icon": "🌐",
          "url": "https://example.com",
          "tags": ["标签1", "标签2"],
          "markdownFile": "nav/data/docs/site-guide.md"
        }
      ]
    }
  ]
}