{"version": "2.0", "description": "FaciShare 导航数据配置文件", "lastUpdated": "2025-08-10", "dataSources": [{"id": "foneshare-faci", "name": "foneshare-faci主配置", "path": "nav/data/foneshare-faci.json", "enabled": false, "priority": 1, "description": "foneshare-faci主配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "localhost"]}, {"id": "foneshare-cloud-saas", "name": "foneshare-cloud-saas主配置", "path": "nav/data/foneshare-cloud-saas.json", "enabled": false, "priority": 2, "description": "foneshare-cloud-saas主配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "localhost"]}, {"id": "foneshare-cloud-private", "name": "foneshare-cloud-private主配置", "path": "nav/data/foneshare-cloud-private.json", "enabled": false, "priority": 3, "description": "foneshare-cloud-private主配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "localhost"]}, {"id": "foneshare-cloud-other", "name": "foneshare-cloud-other主配置", "path": "nav/data/foneshare-cloud-other.json", "enabled": false, "priority": 4, "description": "foneshare-cloud-other主配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "localhost"]}, {"id": "firstshare", "name": "firstshare主配置", "path": "nav/data/firstshare.json", "enabled": true, "priority": 5, "description": "线下firstshare环境主配置", "domains": ["oss.firstshare.cn", "127.0.0.1", "localhost"]}, {"id": "work-tools", "name": "工作常用", "path": "nav/data/work-tools.json", "enabled": true, "priority": 6, "description": "工作常用", "domains": []}], "mergeStrategy": {"duplicateHandling": "merge", "categoryMerging": "append", "siteIdConflict": "<PERSON><PERSON><PERSON><PERSON>", "preserveOrder": true}, "validation": {"strictMode": false, "allowEmptyCategories": true, "requireUniqueIds": true}, "fallback": {"enabled": true, "defaultPath": "nav/data/firstshare.json", "onError": "fallback"}, "timeNotifications": {"enabled": false, "configPath": "data/time-notifications.json", "description": "时间范围自动提示配置", "autoLoad": false, "fallbackEnabled": false}}